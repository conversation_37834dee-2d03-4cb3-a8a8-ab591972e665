-e git+https://github.com/acados/acados.git@9622cfdebdb06a91a4a65bf4fe42adf2a8f16152#egg=acados_template&subdirectory=interfaces/acados_template
action-msgs==1.2.1
action-tutorials-interfaces==0.20.3
action-tutorials-py==0.20.3
actionlib-msgs==4.2.3
ament-cmake-test==1.3.5
ament-copyright==0.12.7
ament-cppcheck==0.12.7
ament-cpplint==0.12.7
ament-flake8==0.12.7
ament-index-python==1.4.0
ament-lint==0.12.7
ament-lint-cmake==0.12.7
ament-package==0.14.0
ament-pep257==0.12.7
ament-uncrustify==0.12.7
ament-xmllint==0.12.7
angles==1.15.0
builtin-interfaces==1.2.1
casadi==3.6.3
certifi==2022.12.7
charset-normalizer==2.1.1
cmake==3.25.0
composition-interfaces==1.2.1
contourpy==1.1.1
cv-bridge==3.2.1
cycler==0.12.0
Cython==3.0.2
demo-nodes-py==0.20.3
diagnostic-msgs==4.2.3
domain-coordinator==0.10.0
example-interfaces==0.9.3
examples-rclpy-executors==0.15.1
examples-rclpy-minimal-action-client==0.15.1
examples-rclpy-minimal-action-server==0.15.1
examples-rclpy-minimal-client==0.15.1
examples-rclpy-minimal-publisher==0.15.1
examples-rclpy-minimal-service==0.15.1
examples-rclpy-minimal-subscriber==0.15.1
filelock==3.9.0
fonttools==4.43.0
future-fstrings==1.2.0
geometry-msgs==4.2.3
gpytorch==1.11
grpcio==1.59.0
idna==3.4
image-geometry==3.2.1
importlib-resources==6.1.0
inputs==0.5
interactive-markers==2.3.2
jaxtyping==0.2.19
Jinja2==3.1.2
joblib==1.3.2
kiwisolver==1.4.5
laser-geometry==2.4.0
launch==1.0.4
launch-ros==0.19.5
launch-testing==1.0.4
launch-testing-ros==0.19.5
launch-xml==1.0.4
launch-yaml==1.0.4
lifecycle-msgs==1.2.1
linear-operator==0.5.2
lit==15.0.7
logging-demo==0.20.3
map-msgs==2.1.0
MarkupSafe==2.1.2
matplotlib==3.7.3
message-filters==4.3.3
mpmath==1.3.0
msgpack-python==0.5.6
msgpack-rpc-python==0.4.1
nav-msgs==4.2.3
networkx==3.0
numpy==1.24.1
opencv-python-headless==********
osrf-pycommon==2.0.2
packaging==23.2
pandas==2.0.3
pcl-msgs==1.0.0
pendulum-msgs==0.20.3
Pillow==9.3.0
pyaml==23.9.7
pygame==2.5.2
pymap3d==3.0.1
pyparsing==3.1.1
PyQt6==6.5.2
PyQt6-Qt6==6.5.2
PyQt6-sip==13.5.2
pyqtgraph==0.13.3
python-dateutil==2.8.2
python-qt-binding==1.1.1
pytz==2023.3.post1
PyYAML==6.0.1
qt-dotgraph==2.2.2
qt-gui==2.2.2
qt-gui-cpp==2.2.2
qt-gui-py-common==2.2.2
quality-of-service-demo-py==0.20.3
rcl-interfaces==1.2.1
rclpy==3.3.9
rcutils==5.1.3
requests==2.28.1
resource-retriever==3.1.1
rmw-dds-common==1.6.0
ros2action==0.18.7
ros2bag==0.15.7
ros2cli==0.18.7
ros2component==0.18.7
ros2doctor==0.18.7
ros2interface==0.18.7
ros2launch==0.19.5
ros2lifecycle==0.18.7
ros2multicast==0.18.7
ros2node==0.18.7
ros2param==0.18.7
ros2pkg==0.18.7
ros2run==0.18.7
ros2service==0.18.7
ros2topic==0.18.7
rosbag2-interfaces==0.15.7
rosbag2-py==0.15.7
rosgraph-msgs==1.2.1
rosidl-adapter==3.1.5
rosidl-cli==3.1.5
rosidl-cmake==3.1.5
rosidl-generator-c==3.1.5
rosidl-generator-cpp==3.1.5
rosidl-generator-py==0.14.4
rosidl-parser==3.1.5
rosidl-runtime-py==0.9.3
rosidl-typesupport-c==2.0.1
rosidl-typesupport-cpp==2.0.1
rosidl-typesupport-fastrtps-c==2.2.1
rosidl-typesupport-fastrtps-cpp==2.2.1
rosidl-typesupport-introspection-c==3.1.5
rosidl-typesupport-introspection-cpp==3.1.5
rpyutils==0.2.1
rqt-action==2.0.1
rqt-bag==1.1.4
rqt-bag-plugins==1.1.4
rqt-console==2.0.2
rqt-graph==1.3.0
rqt-gui==1.1.5
rqt-gui-py==1.1.5
rqt-msg==1.2.0
rqt-plot==1.1.2
rqt-publisher==1.5.0
rqt-py-common==1.1.5
rqt-py-console==1.0.2
rqt-reconfigure==1.1.1
rqt-service-caller==1.0.5
rqt-shell==1.0.2
rqt-srv==1.0.3
rqt-topic==1.5.0
scikit-learn==1.3.1
scipy==1.10.1
sensor-msgs==4.2.3
sensor-msgs-py==4.2.3
shape-msgs==4.2.3
six==1.16.0
sros2==0.10.4
statistics-msgs==1.2.1
std-msgs==4.2.3
std-srvs==4.2.3
stereo-msgs==4.2.3
sympy==1.12
teleop-twist-keyboard==2.3.2
tf2-geometry-msgs==0.25.3
tf2-kdl==0.25.3
tf2-msgs==0.25.3
tf2-py==0.25.3
tf2-ros-py==0.25.3
tf2-tools==0.25.3
threadpoolctl==3.2.0
topic-monitor==0.20.3
torch==2.0.1+cu118
torchaudio==2.0.2+cu118
torchvision==0.15.2+cu118
tornado==4.5.3
tqdm==4.66.1
trajectory-msgs==4.2.3
triton==2.0.0
turtlesim==1.4.2
typeguard==2.13.3
typing_extensions==4.4.0
tzdata==2023.3
unique-identifier-msgs==2.2.1
urllib3==1.26.13
visualization-msgs==4.2.3
zipp==3.17.0
