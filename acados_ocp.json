{"acados_include_path": "/home/<USER>/acados/include", "acados_lib_path": "/home/<USER>/acados/lib", "code_export_directory": "/home/<USER>/autonomous_racing/c_generated_code", "constraints": {"C": [], "C_e": [], "D": [], "constr_type": "BGH", "constr_type_e": "BGH", "has_x0": true, "idxbu": [0, 1, 2], "idxbx": [0, 1, 2, 3, 4, 5, 6, 7, 8], "idxbx_0": [0, 1, 2, 3, 4, 5, 6, 7, 8], "idxbx_e": [0, 1, 2, 3, 4, 5, 6, 7, 8], "idxbxe_0": [0, 1, 2, 3, 4, 5, 6, 7, 8], "idxsbu": [], "idxsbx": [3], "idxsbx_e": [3], "idxsg": [], "idxsg_e": [], "idxsh": [0, 1], "idxsh_e": [0, 1], "idxsphi": [], "idxsphi_e": [], "lbu": [-1.5, -2.0, -17.5], "lbx": [-10000000.0, -10000000.0, -10000000.0, 0.0, -10000000.0, -10000000.0, -0.9, -0.5, -10000000.0], "lbx_0": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "lbx_e": [-10000000.0, -10000000.0, -10000000.0, 0.0, -10000000.0, -10000000.0, -0.9, -0.5, -10000000.0], "lg": [], "lg_e": [], "lh": [0, 0], "lh_e": [0, 0], "lphi": [], "lphi_e": [], "lsbu": [], "lsbx": [0], "lsbx_e": [0], "lsg": [], "lsg_e": [], "lsh": [0, 0], "lsh_e": [0, 0], "lsphi": [], "lsphi_e": [], "ubu": [1.5, 2.0, 17.5], "ubx": [10000000.0, 10000000.0, 10000000.0, 17.5, 10000000.0, 10000000.0, 0.9, 0.5, 10000000.0], "ubx_0": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "ubx_e": [10000000.0, 10000000.0, 10000000.0, 17.5, 10000000.0, 10000000.0, 0.9, 0.5, 10000000.0], "ug": [], "ug_e": [], "uh": [0.5625, 1.0], "uh_e": [0.5625, 0.0], "uphi": [], "uphi_e": [], "usbu": [], "usbx": [0], "usbx_e": [0], "usg": [], "usg_e": [], "ush": [0, 0], "ush_e": [0, 0], "usphi": [], "usphi_e": []}, "cost": {"Vu": [], "Vu_0": [], "Vx": [], "Vx_0": [], "Vx_e": [], "Vz": [], "Vz_0": [], "W": [[0.01, 0.0, 0.0, 0.0, 0.0], [0.0, 100.0, 0.0, 0.0, 0.0], [0.0, 0.0, -0.1, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 0.0, 1.0]], "W_0": [[0.01, 0.0, 0.0, 0.0, 0.0], [0.0, 100.0, 0.0, 0.0, 0.0], [0.0, 0.0, -0.1, 0.0, 0.0], [0.0, 0.0, 0.0, 1.0, 0.0], [0.0, 0.0, 0.0, 0.0, 1.0]], "W_e": [[0.0004, 0.0], [0.0, 4.0]], "Zl": [100000000.0, 100.0, 100.0], "Zl_e": [100000000.0, 100.0, 100.0], "Zu": [10, 100, 100], "Zu_e": [10, 100, 100], "cost_ext_fun_type": "casadi", "cost_ext_fun_type_0": "casadi", "cost_ext_fun_type_e": "casadi", "cost_type": "NONLINEAR_LS", "cost_type_0": "NONLINEAR_LS", "cost_type_e": "NONLINEAR_LS", "yref": [0, 0, 0, 0, 0], "yref_0": [0, 0, 0, 0, 0], "yref_e": [0, 0], "zl": [0.0, 0.0, 0.0], "zl_e": [0.0, 0.0, 0.0], "zu": [0.0, 0.0, 0.0], "zu_e": [0.0, 0.0, 0.0]}, "cython_include_dirs": ["/home/<USER>/anaconda3/envs/autonomous_racing/lib/python3.8/site-packages/numpy/core/include", "/home/<USER>/anaconda3/envs/autonomous_racing/include/python3.8"], "dims": {"N": 40, "nbu": 3, "nbx": 9, "nbx_0": 9, "nbx_e": 9, "nbxe_0": 9, "ng": 0, "ng_e": 0, "nh": 2, "nh_e": 2, "np": 414, "nphi": 0, "nphi_e": 0, "nr": 0, "nr_e": 0, "ns": 3, "ns_0": 2, "ns_e": 3, "nsbu": 0, "nsbx": 1, "nsbx_e": 1, "nsg": 0, "nsg_e": 0, "nsh": 2, "nsh_e": 2, "nsphi": 0, "nsphi_e": 0, "nu": 3, "nx": 9, "ny": 5, "ny_0": 5, "ny_e": 2, "nz": 0}, "json_file": "acados_ocp.json", "model": {"con_h_expr": "@1=(wheel_Df*sin((wheel_Cf*atan((wheel_Bf*((0.436332*steer)-atan2(((0.41*w)+vy),(0.01+vx)))))))), vertcat((sq((x-theta_cx(theta, cx0){0}))+sq((y-theta_cy(theta, cy0){0}))), (sq(((0.00526316*((190*((((car_Tm0+(car_Tm1*(vx/17.5)))+((car_Tm2*(vx/17.5))*throttle))*throttle)-((car_Tr0*(1-tanh((car_Tr1*(vx/17.5)))))+(car_Tr2*sq((vx/17.5))))))-(@1*sin((0.436332*steer)))))/7.5))+sq(((0.00526316*((wheel_Dr*sin((wheel_Cr*atan((wheel_Br*atan2(((0.78*w)-vy),(0.01+vx)))))))+(@1*cos((0.436332*steer)))))/7.5))))", "con_h_expr_e": "vertcat((sq((x-theta_cx(theta, cx0){0}))+sq((y-theta_cy(theta, cy0){0}))), (0.00526316*((wheel_Dr*sin((wheel_Cr*atan((wheel_Br*atan2(((0.78*w)-vy),(0.01+vx)))))))+((wheel_Df*sin((wheel_Cf*atan((wheel_Bf*((0.436332*steer)-atan2(((0.41*w)+vy),(0.01+vx))))))))*cos((0.436332*steer))))))", "con_phi_expr": null, "con_phi_expr_e": null, "con_r_expr": null, "con_r_expr_e": null, "con_r_in_phi": null, "con_r_in_phi_e": null, "cost_conl_custom_outer_hess": null, "cost_conl_custom_outer_hess_0": null, "cost_conl_custom_outer_hess_e": null, "cost_expr_ext_cost": null, "cost_expr_ext_cost_0": null, "cost_expr_ext_cost_custom_hess": null, "cost_expr_ext_cost_custom_hess_0": null, "cost_expr_ext_cost_custom_hess_e": null, "cost_expr_ext_cost_e": null, "cost_psi_expr": null, "cost_psi_expr_0": null, "cost_psi_expr_e": null, "cost_r_in_psi_expr": null, "cost_r_in_psi_expr_0": null, "cost_r_in_psi_expr_e": null, "cost_y_expr": "@1=atan2(theta_cdy(theta, cdy0){0},theta_cdx(theta, cdx0){0}), @2=theta_cx(theta, cx0){0}, @3=theta_cy(theta, cy0){0}, vertcat(((sin(@1)*(x-@2))-(cos(@1)*(y-@3))), ((-(cos(@1)*(x-@2)))-(sin(@1)*(y-@3))), utheta, usteer, uthrottle)", "cost_y_expr_0": "@1=atan2(theta_cdy(theta, cdy0){0},theta_cdx(theta, cdx0){0}), @2=theta_cx(theta, cx0){0}, @3=theta_cy(theta, cy0){0}, vertcat(((sin(@1)*(x-@2))-(cos(@1)*(y-@3))), ((-(cos(@1)*(x-@2)))-(sin(@1)*(y-@3))), utheta, usteer, uthrottle)", "cost_y_expr_e": "@1=atan2(theta_cdy(theta, cdy0){0},theta_cdx(theta, cdx0){0}), @2=theta_cx(theta, cx0){0}, @3=theta_cy(theta, cy0){0}, vertcat(((sin(@1)*(x-@2))-(cos(@1)*(y-@3))), ((-(cos(@1)*(x-@2)))-(sin(@1)*(y-@3))))", "disc_dyn_expr": "@1=((vx-3)/2), @2=((@1*exp((10*@1)))/(1+exp((10*@1)))), @3=((4.53999e-05+(@2*exp((-10*@2))))/(4.53999e-05+exp((-10*@2)))), @4=(190*((((car_Tm0+(car_Tm1*(vx/17.5)))+((car_Tm2*(vx/17.5))*throttle))*throttle)-((car_Tr0*(1-tanh((car_Tr1*(vx/17.5)))))+(car_Tr2*sq((vx/17.5)))))), @5=(wheel_Df*sin((wheel_Cf*atan((wheel_Bf*((0.436332*steer)-atan2(((0.41*w)+vy),(0.01+vx)))))))), @6=(wheel_<PERSON>*sin((wheel_Cr*atan((wheel_Br*atan2(((0.78*w)-vy),(0.01+vx))))))), @7=((@3*vertcat(((vx*cos(hdg))-(vy*sin(hdg))), ((vx*sin(hdg))+(vy*cos(hdg))), w, (0.00526316*((@4-(@5*sin((0.436332*steer))))+((190*vy)*w))), (0.00526316*((@6+(@5*cos((0.436332*steer))))-((190*vx)*w))), ((((0.41*@5)*cos((0.436332*steer)))-(0.78*@6))/car_inertia), 0, 0))+((1-@3)*vertcat(((vx*cos(hdg))-(vy*sin(hdg))), ((vx*sin(hdg))+(vy*cos(hdg))), w, (@4/190), (0.655462*(((0.436332*usteer)*vx)+((0.436332*steer)*(@4/190)))), (0.840336*(((0.436332*usteer)*vx)+((0.436332*steer)*(@4/190)))), 0, 0))), @8=vertcat(hdg, vx, vy, w, steer, throttle), @9=vertcat(car_Tm0, car_Tm1, car_Tm2, car_Tr0, car_Tr1, car_Tr2), @10=vertcat(car_inertia, wheel_Bf, wheel_Cf, wheel_Df, wheel_Br, wheel_Cr, wheel_Dr), @11=f((@8+((dt/2)*@7[2:8])), usteer, @9, @10){0}, @12=f((@8+((dt/2)*@11[2:8])), usteer, @9, @10){0}, (vertcat(x, y, hdg, vx, vy, w, steer, throttle, theta)+vertcat(((dt/6)*(((@7+(2.*@11))+(2.*@12))+f((@8+(dt*@12[2:8])), usteer, @9, @10){0}))[:6], (dt*usteer), (dt*uthrottle), (dt*utheta)))", "dyn_disc_fun": null, "dyn_disc_fun_jac": null, "dyn_disc_fun_jac_hess": null, "dyn_ext_fun_type": "casadi", "dyn_generic_source": null, "f_expl_expr": null, "f_impl_expr": null, "gnsf": {"nontrivial_f_LO": 1, "purely_linear": 0}, "name": "dynamic_bicycle", "p": "vertcat(cx0, cy0, cdx0, cdy0, dt, car_Tm0, car_Tm1, car_Tm2, car_Tr0, car_Tr1, car_Tr2, car_inertia, wheel_Bf, wheel_Cf, wheel_Df, wheel_Br, wheel_Cr, wheel_Dr)", "u": "vertcat(usteer, uthrottle, utheta)", "x": "vertcat(x, y, hdg, vx, vy, w, steer, throttle, theta)", "xdot": null, "z": "0x0"}, "parameter_values": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "problem_class": "OCP", "qp_solver_warm_start": 1, "shared_lib_ext": ".so", "solver_options": {"Tsim": 0.04, "alpha_min": 0.05, "alpha_reduction": 0.7, "collocation_type": "GAUSS_LEGENDRE", "cost_discretization": "EULER", "custom_templates": [], "custom_update_copy": true, "custom_update_filename": "", "custom_update_header_filename": "", "eps_sufficient_descent": 0.0001, "exact_hess_constr": 1, "exact_hess_cost": 1, "exact_hess_dyn": 1, "ext_cost_num_hess": 0, "ext_fun_compile_flags": "-O2", "full_step_dual": 0, "globalization": "FIXED_STEP", "globalization_use_SOC": 0, "hessian_approx": "GAUSS_NEWTON", "hpipm_mode": "SPEED", "initialize_t_slacks": 0, "integrator_type": "DISCRETE", "levenberg_marquardt": 0.01, "line_search_use_sufficient_descent": 0, "model_external_shared_lib_dir": null, "model_external_shared_lib_name": null, "nlp_solver_ext_qp_res": 0, "nlp_solver_max_iter": 100, "nlp_solver_step_length": 1.0, "nlp_solver_tol_comp": 1e-06, "nlp_solver_tol_eq": 1e-06, "nlp_solver_tol_ineq": 1e-06, "nlp_solver_tol_stat": 1e-06, "nlp_solver_type": "SQP_RTI", "print_level": 0, "qp_solver": "PARTIAL_CONDENSING_HPIPM", "qp_solver_cond_N": null, "qp_solver_cond_ric_alg": 1, "qp_solver_iter_max": 50, "qp_solver_ric_alg": 1, "qp_solver_tol_comp": null, "qp_solver_tol_eq": null, "qp_solver_tol_ineq": null, "qp_solver_tol_stat": null, "qp_solver_warm_start": 0, "regularize_method": "PROJECT_REDUC_HESS", "shooting_nodes": null, "sim_method_jac_reuse": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sim_method_newton_iter": 3, "sim_method_newton_tol": 0.0, "sim_method_num_stages": [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], "sim_method_num_steps": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "tf": 1.6, "time_steps": [0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04, 0.04]}, "zoro_description": null}