default:
  image: registry.gitlab.com/trouverun/autonomous_racing:latest

variables:
  GIT_SUBMODULE_STRATEGY: recursive

stages:
  - test
  - deploy

test:
  stage: test
  script:
    #- xvfb-run python3 initial_data_generation.py --sim_path ~/Formula-Student-Driverless-Simulator/FSDS.sh
    - xvfb-run python3 initial_system_identification.py --dataset acceleration_data
    - xvfb-run python3 initial_system_identification.py --dataset steering_data
    - xvfb-run python3 main.py --sim_path ~/Formula-Student-Driverless-Simulator/FSDS.sh --iterations 3 --ci_mode
  timeout: 90 minutes
  artifacts:
    paths:
      - id_results/
      - drivetrain_params.json
      - bicycle_params.json
      - dynamics_data/
      - results/
      - ci_result.json
    expire_in: 1 week
  only:
    - revert

pages:
  stage: deploy
  dependencies:
    - test
  environment: production
  script:
    - python3 -m pip install python-gitlab
    - python3 render_results.py --key $PRIVATE_KEY
    - mkdir public
    - cp index.html public
    - cp -R static/ public/
    - cp -R results/ public/
    - cp -R ci_plots/ public/
  artifacts:
    paths:
      - public/
      - ci_result.json
    expire_in: 1 week
  only:
    - revert

